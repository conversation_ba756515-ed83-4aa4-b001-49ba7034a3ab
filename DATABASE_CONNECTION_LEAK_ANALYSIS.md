# Nymbl API Database Connection Leak Analysis

**Author:** Augment Code AI Assistant  
**Date:** December 2024  
**Severity:** CRITICAL  

## Executive Summary

This comprehensive analysis identifies critical database connection leaks in the Nymbl API codebase causing production connection pool exhaustion. The analysis reveals a combination of architectural issues, manual resource management patterns, and configuration problems that prevent proper connection cleanup, leading to connection accumulation that outpaces automated cleanup mechanisms.

## Critical Findings Overview

| Severity | Issue Count | Primary Cause | Impact |
|----------|-------------|---------------|---------|
| CRITICAL | 5 | Hibernate lazy loading + manual resource management | Connection pool exhaustion |
| HIGH | 8 | EntityManager leaks + batch job issues | Memory leaks + connection retention |
| MEDIUM | 6 | Configuration issues + interceptor patterns | Intermittent connection issues |

## 🚨 ROOT CAUSE IDENTIFIED

**ENABLE_LAZY_LOAD_NO_TRANS = true** - This Hibernate configuration setting is the primary culprit enabling unlimited connection creation outside Spring's transaction management.

## Production Symptoms Explained

- **New connections outpace cleanup** ✅ Confirmed - Lazy loading + manual leaks bypass eviction
- **Eviction not happening** ✅ Confirmed - Leaked resources appear "active" to connection pools  
- **Min evict idle settings ineffective** ✅ Confirmed - Connections created outside pool management

## 🚨 CRITICAL CONNECTION LEAK VULNERABILITIES

### 1. Hibernate Lazy Loading Configuration - ROOT CAUSE
**Location:** `MasterDatabaseConfig.java` and `TenantDatabaseConfig.java`  
**Risk Level:** CRITICAL  
**Leak Probability:** Continuous during normal operations  

```java
// BOTH Master and Tenant configurations contain this dangerous setting:
properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "true");
```

**Problem Analysis:**
- **Uncontrolled Connection Creation:** Allows Hibernate to open new sessions/connections for lazy loading outside transactions
- **Bypasses Spring Transaction Management:** These connections are not managed by Spring's transaction lifecycle
- **Amplified by Multi-Tenancy:** Each tenant can trigger lazy loading connections independently
- **No Automatic Cleanup:** Connections created outside transaction boundaries may not be properly released
- **Explains Production Symptoms:** This setting allows connections to accumulate faster than any eviction mechanism can clean them

### 2. DataDictionaryService Raw JDBC - IMMEDIATE THREAT
**Location:** `src/main/java/com/nymbl/config/service/DataDictionaryService.java:49-63`  
**Risk Level:** CRITICAL  
**Leak Probability:** 100% on any exception  

```java
public String generate() throws Exception {
    String fullURL = String.format(url.concat("&user=%s&password=%s"), username, password);
    Connection connection = DriverManager.getConnection(fullURL);  // ❌ NO try-with-resources
    Statement statement = connection.createStatement();
    ResultSet rs = statement.executeQuery(sql);
    // ... processing ...
    rs.close();      // ❌ Manual close - leaks if exception occurs
    statement.close();
    connection.close();
}
```

**Problem Analysis:**
- **Raw JDBC Outside Pool:** Bypasses all connection pool configurations and eviction settings
- **No Exception Handling:** Any exception between `getConnection()` and `connection.close()` causes permanent leak
- **Direct Database Access:** Creates connections directly to database without pool management
- **Guaranteed Leak Scenario:** 100% leak probability during exception conditions

### 3. Spring Batch Connection Pool Abuse - HIGH RISK
**Location:** Multiple Batch Configuration Classes  
**Risk Level:** HIGH  
**Pattern:** Direct DataSource access with massive page sizes  

```java
@Bean
@StepScope
public JdbcPagingItemReader<QuicksightPrescriptionSummary> qsPrescriptionSummaryJdbcPagingItemReader(@Value("#{jobParameters['tenant']}") String tenant) {
    JdbcPagingItemReader<QuicksightPrescriptionSummary> reader = new JdbcPagingItemReader<>();
    reader.setDataSource(MultiTenantConnectionProviderImpl.map.get(tenant));  // ❌ Direct map access
    reader.setPageSize(50000);  // ❌ MASSIVE page size
    reader.setFetchSize(50000); // ❌ MASSIVE fetch size
}
```

**Affected Classes:**
- `PrescriptionSummaryBatchConfiguration.java`
- `GeneralLedgerBatchConfiguration.java`
- `UserTaskBatchConfiguration.java`
- `AiNotesUsageBatchConfiguration.java`
- `NymblStatusHistoryBatchConfiguration.java`
- `PurchasingHistoryBatchConfiguration.java`
- `ClinicalOperationsBatchConfiguration.java`

**Problem Analysis:**
- **Direct DataSource Map Access:** Bypasses Spring's connection management
- **Massive Page/Fetch Sizes:** 50,000 records per page can hold connections for extended periods
- **No Connection Timeout Configuration:** Batch jobs can hold connections indefinitely
- **Multi-Tenant Amplification:** Each tenant runs separate batch jobs, multiplying connection usage

### 4. Scheduled Job Connection Storms - CRITICAL RISK
**Location:** `src/main/java/com/nymbl/config/cron/DailyJobExecutor.java`  
**Risk Level:** CRITICAL  
**Pattern:** Simultaneous multi-tenant operations without connection limits  

```java
List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
for (Company company : companies) {
    TenantContext.setCurrentTenant(company.getKey());
    prescriptionService.billDmeRentals(DateUtil.getCurrentDate());  // ❌ Heavy DB operations per tenant
    TenantContext.clear();
}
```

**Affected Scheduled Jobs:**
- `userPermissionsJob()` - 3:00 AM daily
- `patientRecallJob()` - Multiple timezone-based schedules
- `billDmeRentalsJob()` - Multiple timezone-based schedules
- `timelyFilingJob()` - Multiple timezone-based schedules
- `dueTasksJob()` - Multiple timezone-based schedules
- `clinicalOperationsJob()` - Multiple timezone-based schedules

**Problem Analysis:**
- **Connection Storm Pattern:** Multiple scheduled jobs run simultaneously across all tenants
- **No Connection Throttling:** Each tenant operation can consume full connection pool
- **Cascading Failures:** If one tenant's connections leak, it affects subsequent tenants
- **Peak Load Amplification:** All cron jobs trigger at same time zones

### 5. UserService Session Management - HIGH RISK
**Location:** `src/main/java/com/nymbl/master/service/UserService.java:1030-1045, 1054-1075`  
**Risk Level:** CRITICAL  
**Leak Probability:** High during exceptions  

```java
public List<QuicksightAssetUserPermissions> getUserAssetPermissions() {
    EntityManager em = masterEntityManager.getEntityManagerFactory().createEntityManager();
    Session session = em.unwrap(Session.class);
    session.doWork(connection -> {
        CallableStatement stmt = connection.prepareCall("{call userAssetPermissions()}");
        // ... processing ...
        connection.close();  // ❌ DANGEROUS: Manual close within doWork!
    });
    em.close();  // ❌ Not in try-with-resources
}
```

**Problem Analysis:**
- **Manual EntityManager Creation:** Without proper exception handling
- **Dangerous connection.close():** Within session.doWork() - Hibernate manages connection lifecycle
- **CallableStatement Leaks:** Not using try-with-resources
- **EntityManager Leak:** If exception occurs before em.close()

## 🔍 HIGH-RISK PATTERNS IDENTIFIED

### 6. GeneralLedgerComparisonRepository - SYSTEMATIC LEAKS
**Location:** `src/main/java/com/nymbl/tenant/repository/GeneralLedgerComparisonRepository.java`  
**Risk Level:** HIGH  
**Affected Methods:** 8+ methods with identical pattern  

```java
public long getLastRevisionId() {
    EntityManager em = tenantEntityManager.getEntityManagerFactory().createEntityManager();
    // ... query execution ...
    em.close();  // ❌ Not in finally block or try-with-resources
    return result;
}
```

**Problem Analysis:**
- **Systematic Pattern:** Across multiple methods: `getQueryResultLongList()`, `checkPrescriptionRevisionId()`, `resetCachedLiveGL()`
- **EntityManager Creation:** Without exception safety
- **Transaction Management:** Without proper cleanup
- **Each Method:** Represents a potential leak point

### 7. AbstractTableService - KNOWN LEAK ACKNOWLEDGMENT
**Location:** `src/main/java/com/nymbl/config/service/AbstractTableService.java:407-415`  
**Risk Level:** HIGH  
**Evidence:** Developer comment indicates awareness  

```java
} catch (Exception e) {
    //em.close(); leaks...    // ❌ COMMENT SHOWS KNOWN ISSUE
    log.error(StringUtil.getExceptionAsString(e));
    Sentry.captureException(e);
}
```

**Problem Analysis:**
- **Known Issue:** Comment `//em.close(); leaks...` indicates developers are aware of leak issue
- **Multiple Methods:** In this class create EntityManager instances manually
- **Base Class:** Used by many services, multiplying impact
- **Unresolved:** Issue acknowledged but not fixed

### 8. TenantIdentifierInterceptor Database Access - MEDIUM-HIGH RISK
**Location:** `src/main/java/com/nymbl/tenant/TenantIdentifierInterceptorAdapter.java:35-47`  
**Risk Level:** MEDIUM-HIGH  
**Pattern:** Database access in HTTP request interceptor  

```java
@Override
public boolean preHandle(HttpServletRequest req, HttpServletResponse res, Object handler) throws IOException {
    String authToken = req.getHeader(AuthoritiesConstants.X_AUTH_TOKEN);
    String username = tokenUtils.getUsernameFromToken(authToken);
    User user = username != null ? userRepository.findByUsername(username) : null;  // ❌ DB access in interceptor
    Company company = tokenUtils.getCompanyFromToken(authToken);
    // ... tenant context setup
}
```

**Problem Analysis:**
- **Database Access Per Request:** Every HTTP request triggers a database lookup for user
- **No Connection Timeout:** Interceptor database access not subject to request timeouts
- **High Frequency:** Can generate thousands of database connections during peak load
- **No Caching:** User lookup happens on every request without caching

### 9. Connection Pool Configuration Issues
**Location:** `src/main/java/com/nymbl/config/MasterDatabaseConfig.java`  
**Risk Level:** MEDIUM  
**Pattern:** Extreme pool size differences between environments  

```java
if (Arrays.asList(environment.getActiveProfiles()).contains("local")) {
    config.setMaximumPoolSize(20);      // Local: 20 connections
    config.setMinimumIdle(5);
} else {
    config.setMaximumPoolSize(1_000);   // Production: 1,000 connections ❌
    config.setMinimumIdle(50);
}
```

**Problem Analysis:**
- **Massive Production Pool:** 1,000 max connections per master database
- **Per-Tenant Multiplication:** Each tenant also gets large pools
- **Resource Exhaustion Risk:** Database server may not support total connection load
- **Leak Amplification:** Large pools mask leak detection

### 10. Manual Transaction Management in Repositories - HIGH RISK
**Location:** `src/main/java/com/nymbl/tenant/repository/GeneralLedgerStaticRepository.java`  
**Risk Level:** HIGH  
**Pattern:** Multiple @Transactional @Modifying operations without proper connection management  

```java
@Modifying
@Transactional
@Query(value = "create table cached_nightly_gl as select * from general_ledger where gl_year is not null", nativeQuery = true)
Integer createSnapshotCachedNightlyCopy();

@Modifying
@Transactional
@Query(value = "drop table if exists cached_nightly_gl", nativeQuery = true)
Integer dropExistingCachedNightlyCopy();
```

**Problem Analysis:**
- **DDL Operations in Transactions:** CREATE TABLE and DROP TABLE operations can hold connections longer
- **Multiple Sequential Operations:** Service layer calls multiple repository methods sequentially
- **No Connection Pooling for DDL:** DDL operations may not benefit from connection pooling optimizations
- **Potential Deadlocks:** Multiple DDL operations across tenants can cause database locks

## 📊 LEAK IMPACT ANALYSIS

### Connection Pool Exhaustion Mechanics

1. **Lazy Loading Override Pool Settings**
   - ENABLE_LAZY_LOAD_NO_TRANS allows unlimited connection creation outside transaction boundaries
   - Each lazy property access can open new connections that bypass pool management
   - Connections created outside Spring's transaction management may not be properly tracked

2. **Manual Resource Leaks Override Pool Settings**
   - Eviction only works on idle connections
   - Leaked connections remain "active" in pool tracking
   - removeAbandoned cannot detect properly closed but leaked EntityManagers

3. **Multi-Tenant Amplification**
   - Each tenant has separate connection pool
   - Leak in one tenant affects that tenant's pool
   - Cross-tenant operations multiply leak potential
   - Batch jobs and cron jobs run per tenant, multiplying connection usage

4. **Exception-Driven Leak Acceleration**
   - Production exceptions trigger leak conditions
   - High-load scenarios increase exception probability
   - Leaked connections accumulate faster than eviction can clean

### Connection Leak Multiplication Factors

- **Multi-Tenant Architecture:** Each issue multiplied by number of active tenants
- **Lazy Loading:** Each entity access can trigger new connections
- **Batch Operations:** Large page sizes hold connections for extended periods
- **Cron Jobs:** Simultaneous execution across all tenants
- **HTTP Request Volume:** Database access in interceptors scales with traffic

## 🎯 IMMEDIATE ACTION PLAN

### CRITICAL (Deploy Today):

1. **Disable Lazy Loading Outside Transactions - HIGHEST PRIORITY**
   ```java
   // Change in both MasterDatabaseConfig.java and TenantDatabaseConfig.java
   properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "false");
   ```

2. **Fix DataDictionaryService Raw JDBC**
   ```java
   public String generate() throws Exception {
       String fullURL = String.format(url.concat("&user=%s&password=%s"), username, password);
       try (Connection connection = DriverManager.getConnection(fullURL);
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql)) {
           // ... processing ...
           return buildHTML(dbResults);
       }
   }
   ```

3. **Reduce Batch Page Sizes**
   ```java
   // Change from 50,000 to 5,000 maximum in all batch configurations
   reader.setPageSize(5000);
   reader.setFetchSize(5000);
   ```

### HIGH PRIORITY (This Week):

4. **Fix UserService CallableStatement Patterns**
   ```java
   session.doWork(connection -> {
       try (CallableStatement stmt = connection.prepareCall("{call userAssetPermissions()}")) {
           // ... processing ...
           // ❌ REMOVE: connection.close(); - Let Hibernate manage!
       }
   });
   ```

5. **Fix GeneralLedgerComparisonRepository EntityManager Leaks**
   ```java
   public long getLastRevisionId() {
       try (EntityManager em = tenantEntityManager.getEntityManagerFactory().createEntityManager()) {
           // ... query execution ...
           return result;
       }
   }
   ```

6. **Add User Caching in TenantIdentifierInterceptor**
   - Implement user lookup caching to reduce database calls per request

7. **Audit Connection Pool Sizes**
   - Reduce production pool sizes from 1,000 to 100 and tune up based on monitoring

### MONITORING (Immediate):

8. **Enable Connection Leak Detection**
   ```properties
   # HikariCP
   leakDetectionThreshold=60000
   # Tomcat JDBC
   logAbandoned=true
   removeAbandonedTimeout=300
   ```

9. **Add Connection Pool Monitoring**
   ```properties
   # Add to application.properties
   logging.level.org.apache.tomcat.jdbc.pool=DEBUG
   logging.level.com.zaxxer.hikari=DEBUG
   ```

## 🔧 CONFIGURATION RECOMMENDATIONS

### Immediate Pool Tuning
```properties
# Reduce abandoned timeout for faster leak detection
removeAbandonedTimeout=300
# Enable abandoned connection logging
logAbandoned=true
# More aggressive eviction
timeBetweenEvictionRunsMillis=15000
minEvictableIdleTimeMillis=30000
```

### HikariCP Migration Priority
- HikariCP has superior leak detection and recovery
- Current local setting: `nymbl.database.hikari.enabled=true`
- Recommend production migration to HikariCP

## 📈 SUCCESS METRICS

1. **Connection Pool Utilization** - Target: <70% peak usage
2. **Abandoned Connection Count** - Target: 0 per hour
3. **EntityManager Leak Rate** - Target: 0 detected leaks
4. **Application Error Rate** - Monitor for connection timeout errors
5. **Lazy Loading Connection Creation** - Monitor after disabling ENABLE_LAZY_LOAD_NO_TRANS

## 🚨 FINAL RISK ASSESSMENT

**Current State:** CRITICAL - Production connection exhaustion imminent
**Root Cause:** ENABLE_LAZY_LOAD_NO_TRANS + manual resource management patterns
**Post-Fix State:** LOW - With proper resource management and lazy loading disabled
**Deployment Risk:** LOW - Fixes are conservative resource management improvements

**Recommendation:** Deploy CRITICAL fixes immediately to prevent production outage. The ENABLE_LAZY_LOAD_NO_TRANS setting is likely the primary cause of your connection leak issues and should be disabled first.
