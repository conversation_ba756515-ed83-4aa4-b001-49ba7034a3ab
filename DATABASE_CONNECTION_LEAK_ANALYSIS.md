# Nymbl API Database Connection Leak Analysis

**Author:** <PERSON>
**Date:** June 4, 2025

## Executive Summary

This comprehensive analysis identifies critical database connection leaks in the Nymbl API codebase that are causing production connection pool exhaustion. Despite configured connection pool eviction settings, manual resource management patterns are preventing proper cleanup, leading to connection accumulation that outpaces any automated cleanup mechanisms.

## Critical Findings Overview

| Severity | Issue Count | Primary Cause | Impact |
|----------|-------------|---------------|---------|
| CRITICAL | 3 | Manual resource management without try-with-resources | Connection pool exhaustion |
| HIGH | 8 | EntityManager leaks in exception scenarios | Memory leaks + connection retention |
| MEDIUM | 5 | Improper session.doWork() patterns | Intermittent connection issues |

## 🚨 CRITICAL CONNECTION LEAK VULNERABILITIES

### 1. DataDictionaryService - IMMEDIATE THREAT
**Location:** `src/main/java/com/nymbl/config/service/DataDictionaryService.java:49-63`  
**Risk Level:** CRITICAL  
**Leak Probability:** 100% on any exception  

```java
public String generate() throws Exception {
    String fullURL = String.format(url.concat("&user=%s&password=%s"), username, password);
    Connection connection = DriverManager.getConnection(fullURL);  // ❌ NO try-with-resources
    Statement statement = connection.createStatement();
    ResultSet rs = statement.executeQuery(sql);
    // ... processing ...
    rs.close();      // ❌ Manual close - leaks if exception occurs
    statement.close();
    connection.close();
}
```

**Problem Analysis:**
- Raw JDBC connection management outside connection pool
- No exception handling for resource cleanup
- Any exception between `getConnection()` and `connection.close()` causes permanent leak
- This method bypasses all connection pool configurations and eviction settings

### 2. UserService Session Management - HIGH RISK
**Location:** `src/main/java/com/nymbl/master/service/UserService.java:1030-1045, 1054-1075`  
**Risk Level:** CRITICAL  
**Leak Probability:** High during exceptions  

```java
public List<QuicksightAssetUserPermissions> getUserAssetPermissions() {
    EntityManager em = masterEntityManager.getEntityManagerFactory().createEntityManager();
    Session session = em.unwrap(Session.class);
    session.doWork(connection -> {
        CallableStatement stmt = connection.prepareCall("{call userAssetPermissions()}");
        // ... processing ...
        connection.close();  // ❌ DANGEROUS: Manual close within doWork!
    });
    em.close();  // ❌ Not in try-with-resources
}
```

**Problem Analysis:**
- Manual `EntityManager` creation without proper exception handling
- Dangerous `connection.close()` within `session.doWork()` - Hibernate manages connection lifecycle
- `CallableStatement` not using try-with-resources
- EntityManager leak if exception occurs before `em.close()`

### 3. GeneralLedgerComparisonRepository - SYSTEMATIC LEAKS
**Location:** `src/main/java/com/nymbl/tenant/repository/GeneralLedgerComparisonRepository.java`  
**Risk Level:** HIGH  
**Affected Methods:** 8+ methods with identical pattern  

```java
public long getLastRevisionId() {
    EntityManager em = tenantEntityManager.getEntityManagerFactory().createEntityManager();
    // ... query execution ...
    em.close();  // ❌ Not in finally block or try-with-resources
    return result;
}
```

**Problem Analysis:**
- Systematic pattern across multiple methods: `getQueryResultLongList()`, `checkPrescriptionRevisionId()`, `resetCachedLiveGL()`
- EntityManager creation without exception safety
- Transaction management without proper cleanup
- Each method represents a potential leak point

## 🔍 HIGH-RISK PATTERNS IDENTIFIED

### 4. AbstractTableService - KNOWN LEAK ACKNOWLEDGMENT
**Location:** `src/main/java/com/nymbl/config/service/AbstractTableService.java:407-415`  
**Risk Level:** HIGH  
**Evidence:** Developer comment indicates awareness  

```java
} catch (Exception e) {
    //em.close(); leaks...    // ❌ COMMENT SHOWS KNOWN ISSUE
    log.error(StringUtil.getExceptionAsString(e));
    Sentry.captureException(e);
}
```

**Problem Analysis:**
- Comment `//em.close(); leaks...` indicates developers are aware of leak issue
- Multiple methods in this class create EntityManager instances manually
- `compareAudits()` and `getAuditDetails()` methods follow same unsafe pattern
- Base class used by many services, multiplying impact

### 5. Connection Pool Configuration Issues
**Location:** `src/main/java/com/nymbl/tenant/MultiTenantConnectionProviderImpl.java:167-191`  
**Risk Level:** MEDIUM-HIGH  
**Configuration Problems:**

```java
// Tomcat JDBC Pool Settings
ds.setRemoveAbandonedTimeout(600);  // 10 minutes - too long for high load
ds.setTimeBetweenEvictionRunsMillis(30_000);  // 30 seconds
ds.setMinEvictableIdleTimeMillis(60_000);     // 1 minute
```

**Problem Analysis:**
- `removeAbandonedTimeout` of 600 seconds (10 minutes) too long for production load
- Eviction settings cannot compensate for manual resource leaks
- HikariCP vs Tomcat JDBC inconsistent configurations
- Per-tenant connection pools multiply leak impact

## 📊 LEAK IMPACT ANALYSIS

### Connection Pool Exhaustion Mechanics

1. **Manual Resource Leaks Override Pool Settings**
   - Eviction only works on idle connections
   - Leaked connections remain "active" in pool tracking
   - `removeAbandoned` cannot detect properly closed but leaked EntityManagers

2. **Multi-Tenant Amplification**
   - Each tenant has separate connection pool
   - Leak in one tenant affects that tenant's pool
   - Cross-tenant operations multiply leak potential

3. **Exception-Driven Leak Acceleration**
   - Production exceptions trigger leak conditions
   - High-load scenarios increase exception probability
   - Leaked connections accumulate faster than eviction can clean

### Production Symptoms Correlation

- **New connections outpace cleanup** ✅ Confirmed - Manual leaks bypass eviction
- **Eviction not happening** ✅ Confirmed - Leaked resources appear "active"
- **Min evict idle settings ineffective** ✅ Confirmed - Leaks prevent idle state

## 🎯 IMMEDIATE ACTION PLAN

### Phase 1: Critical Fixes (Deploy Immediately)

1. **DataDictionaryService.generate()** - HIGHEST PRIORITY
   ```java
   // FIXED VERSION
   public String generate() throws Exception {
       String fullURL = String.format(url.concat("&user=%s&password=%s"), username, password);
       try (Connection connection = DriverManager.getConnection(fullURL);
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql)) {
           // ... processing ...
           return buildHTML(dbResults);
       }
   }
   ```

2. **UserService CallableStatement Patterns**
   ```java
   // FIXED VERSION
   session.doWork(connection -> {
       try (CallableStatement stmt = connection.prepareCall("{call userAssetPermissions()}")) {
           // ... processing ...
           // ❌ REMOVE: connection.close(); - Let Hibernate manage!
       }
   });
   ```

### Phase 2: Systematic Fixes (Next Sprint)

3. **GeneralLedgerComparisonRepository** - Refactor all methods
4. **AbstractTableService** - Fix EntityManager patterns
5. **Connection Pool Tuning** - Reduce abandoned timeout to 300 seconds

### Phase 3: Monitoring & Prevention

6. **Connection Leak Detection**
   ```properties
   # Add to application.properties
   logging.level.org.apache.tomcat.jdbc.pool=DEBUG
   ```

7. **JMX Monitoring Implementation**
8. **Code Review Guidelines** - Mandatory try-with-resources for all DB resources

## 🔧 CONFIGURATION RECOMMENDATIONS

### Immediate Pool Tuning
```properties
# Reduce abandoned timeout for faster leak detection
removeAbandonedTimeout=300
# Enable abandoned connection logging
logAbandoned=true
# More aggressive eviction
timeBetweenEvictionRunsMillis=15000
minEvictableIdleTimeMillis=30000
```

### HikariCP Migration Priority
- HikariCP has superior leak detection and recovery
- Current local setting: `nymbl.database.hikari.enabled=true`
- Recommend production migration to HikariCP

## 📈 SUCCESS METRICS

1. **Connection Pool Utilization** - Target: <70% peak usage
2. **Abandoned Connection Count** - Target: 0 per hour
3. **EntityManager Leak Rate** - Target: 0 detected leaks
4. **Application Error Rate** - Monitor for connection timeout errors

## 🚨 RISK ASSESSMENT

**Current State:** CRITICAL - Production connection exhaustion imminent  
**Post-Fix State:** LOW - With proper resource management  
**Deployment Risk:** LOW - Fixes are conservative resource management improvements  

**Recommendation:** Deploy Phase 1 fixes immediately to prevent production outage.

---

## 🔍 ADDITIONAL CRITICAL FINDINGS - NOT COVERED IN ORIGINAL ANALYSIS

### 6. Spring Batch Connection Pool Abuse - HIGH RISK
**Location:** Multiple Batch Configuration Classes
**Risk Level:** HIGH
**Pattern:** Direct DataSource access bypassing Spring's connection management

<augment_code_snippet path="src/main/java/com/nymbl/tenant/batch/config/PrescriptionSummaryBatchConfiguration.java" mode="EXCERPT">
````java
@Bean
@StepScope
public JdbcPagingItemReader<QuicksightPrescriptionSummary> qsPrescriptionSummaryJdbcPagingItemReader(@Value("#{jobParameters['tenant']}") String tenant) {
    JdbcPagingItemReader<QuicksightPrescriptionSummary> reader = new JdbcPagingItemReader<>();
    reader.setDataSource(MultiTenantConnectionProviderImpl.map.get(tenant));  // ❌ Direct map access
    reader.setPageSize(50000);  // ❌ MASSIVE page size
    reader.setFetchSize(50000); // ❌ MASSIVE fetch size
````
</augment_code_snippet>

**Affected Classes:**
- `PrescriptionSummaryBatchConfiguration.java`
- `GeneralLedgerBatchConfiguration.java`
- `UserTaskBatchConfiguration.java`
- `AiNotesUsageBatchConfiguration.java`
- `NymblStatusHistoryBatchConfiguration.java`
- `PurchasingHistoryBatchConfiguration.java`
- `ClinicalOperationsBatchConfiguration.java`

**Problem Analysis:**
- **Direct DataSource Map Access:** `MultiTenantConnectionProviderImpl.map.get(tenant)` bypasses Spring's connection management
- **Massive Page/Fetch Sizes:** 50,000 records per page can hold connections for extended periods
- **No Connection Timeout Configuration:** Batch jobs can hold connections indefinitely
- **Multi-Tenant Amplification:** Each tenant runs separate batch jobs, multiplying connection usage

### 7. Scheduled Job Connection Storms - CRITICAL RISK
**Location:** `src/main/java/com/nymbl/config/cron/DailyJobExecutor.java`
**Risk Level:** CRITICAL
**Pattern:** Simultaneous multi-tenant operations without connection limits

<augment_code_snippet path="src/main/java/com/nymbl/config/cron/DailyJobExecutor.java" mode="EXCERPT">
````java
List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
for (Company company : companies) {
    TenantContext.setCurrentTenant(company.getKey());
    prescriptionService.billDmeRentals(DateUtil.getCurrentDate());  // ❌ Heavy DB operations per tenant
    TenantContext.clear();
}
````
</augment_code_snippet>

**Problem Analysis:**
- **Connection Storm Pattern:** Multiple scheduled jobs (`@Scheduled`) run simultaneously across all tenants
- **No Connection Throttling:** Each tenant operation can consume full connection pool
- **Cascading Failures:** If one tenant's connections leak, it affects subsequent tenants
- **Peak Load Amplification:** All cron jobs trigger at same time zones

**Affected Scheduled Jobs:**
- `userPermissionsJob()` - 3:00 AM daily
- `patientRecallJob()` - Multiple timezone-based schedules
- `billDmeRentalsJob()` - Multiple timezone-based schedules
- `timelyFilingJob()` - Multiple timezone-based schedules
- `dueTasksJob()` - Multiple timezone-based schedules
- `clinicalOperationsJob()` - Multiple timezone-based schedules

### 8. AbstractTableService Direct DataSource Usage - MEDIUM-HIGH RISK
**Location:** `src/main/java/com/nymbl/config/service/AbstractTableService.java:885-909`
**Risk Level:** MEDIUM-HIGH
**Pattern:** Proper try-with-resources BUT redundant manual close

<augment_code_snippet path="src/main/java/com/nymbl/config/service/AbstractTableService.java" mode="EXCERPT">
````java
DataSource dataSource = MultiTenantConnectionProviderImpl.map.get(TenantContext.getCurrentTenant());
try (Connection conn = dataSource.getConnection();
     Statement statement = conn.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
     ResultSet rs = statement.executeQuery(sql)) {
    // ... processing ...
    rs.close();  // ❌ REDUNDANT: try-with-resources already handles this
}
````
</augment_code_snippet>

**Problem Analysis:**
- **Good:** Uses try-with-resources correctly
- **Issue:** Redundant `rs.close()` call - unnecessary but not harmful
- **Concern:** Direct DataSource map access pattern spreads throughout codebase

### 9. Connection Pool Size Mismatches - CONFIGURATION RISK
**Location:** `src/main/java/com/nymbl/config/MasterDatabaseConfig.java`
**Risk Level:** MEDIUM
**Pattern:** Extreme pool size differences between environments

<augment_code_snippet path="src/main/java/com/nymbl/config/MasterDatabaseConfig.java" mode="EXCERPT">
````java
if (Arrays.asList(environment.getActiveProfiles()).contains("local")) {
    config.setMaximumPoolSize(20);      // Local: 20 connections
    config.setMinimumIdle(5);
} else {
    config.setMaximumPoolSize(1_000);   // Production: 1,000 connections ❌
    config.setMinimumIdle(50);
}
````
</augment_code_snippet>

**Problem Analysis:**
- **Massive Production Pool:** 1,000 max connections per master database
- **Per-Tenant Multiplication:** Each tenant also gets large pools
- **Resource Exhaustion Risk:** Database server may not support total connection load
- **Leak Amplification:** Large pools mask leak detection

### 10. Cron Profile Connection Pool Inconsistency - MEDIUM RISK
**Location:** `src/main/java/com/nymbl/tenant/MultiTenantConnectionProviderImpl.java:167-179`
**Risk Level:** MEDIUM
**Pattern:** Different connection settings for cron profile

<augment_code_snippet path="src/main/java/com/nymbl/tenant/MultiTenantConnectionProviderImpl.java" mode="EXCERPT">
````java
if (Arrays.asList(environment.getActiveProfiles()).contains("cron")) {
    ds.setMaxActive(50);
    ds.setRemoveAbandonedTimeout(1200);  // 20 minutes for cron ❌
} else {
    ds.setMaxActive(company.getDatabaseConnections());
    ds.setRemoveAbandonedTimeout(600);   // 10 minutes for normal
}
````
</augment_code_snippet>

**Problem Analysis:**
- **Longer Timeout for Cron:** 20 minutes vs 10 minutes allows leaks to persist longer
- **Batch Job Impact:** Cron jobs with leaks hold connections for 20 minutes
- **Inconsistent Behavior:** Different leak detection timing between profiles

## 🚨 NEWLY IDENTIFIED CRITICAL RISKS

### Batch Job Connection Exhaustion
**Immediate Threat:** Spring Batch jobs with 50,000 record page sizes can hold connections for minutes/hours
**Impact:** Single batch job can exhaust entire tenant connection pool
**Mitigation:** Reduce page sizes to 1,000-5,000 records maximum

### Scheduled Job Connection Storms
**Immediate Threat:** Multiple cron jobs running simultaneously across all tenants
**Impact:** Peak load can exhaust all connection pools simultaneously
**Mitigation:** Implement connection throttling and job scheduling staggering

### Production Pool Size Risk
**Immediate Threat:** 1,000 connection pools × multiple tenants = potential database server overload
**Impact:** Database server connection limit exhaustion
**Mitigation:** Audit actual connection usage and reduce pool sizes

## 🎯 UPDATED IMMEDIATE ACTION PLAN

### Phase 1A: Batch Job Fixes (URGENT)
1. **Reduce Batch Page Sizes** - Change from 50,000 to 5,000 maximum
2. **Add Batch Connection Timeouts** - Implement 5-minute timeouts for batch operations
3. **Audit Cron Job Scheduling** - Stagger job execution to prevent connection storms

### Phase 1B: Pool Size Audit (URGENT)
1. **Monitor Actual Connection Usage** - Implement connection pool monitoring
2. **Reduce Production Pool Sizes** - Start with 100 max connections and tune up
3. **Standardize Cron Timeouts** - Use consistent 300-second timeout across profiles

---

## 🔍 FINAL CRITICAL FINDINGS - ARCHITECTURAL ISSUES

### 11. Hibernate Lazy Loading Without Transactions - CRITICAL RISK
**Location:** Both `MasterDatabaseConfig.java` and `TenantDatabaseConfig.java`
**Risk Level:** CRITICAL
**Pattern:** Global lazy loading outside transactions enabled

<augment_code_snippet path="src/main/java/com/nymbl/config/MasterDatabaseConfig.java" mode="EXCERPT">
````java
properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "true");
````
</augment_code_snippet>

<augment_code_snippet path="src/main/java/com/nymbl/config/TenantDatabaseConfig.java" mode="EXCERPT">
````java
properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "true");
````
</augment_code_snippet>

**Problem Analysis:**
- **ENABLE_LAZY_LOAD_NO_TRANS = true** allows Hibernate to open new sessions/connections for lazy loading outside transactions
- **Uncontrolled Connection Creation:** Each lazy property access can create new database connections
- **No Connection Lifecycle Management:** These connections may not be properly managed by Spring's transaction management
- **Amplified by Multi-Tenancy:** Each tenant can trigger lazy loading connections independently

### 12. Manual Transaction Management in Repositories - HIGH RISK
**Location:** `src/main/java/com/nymbl/tenant/repository/GeneralLedgerStaticRepository.java`
**Risk Level:** HIGH
**Pattern:** Multiple @Transactional @Modifying operations without proper connection management

<augment_code_snippet path="src/main/java/com/nymbl/tenant/repository/GeneralLedgerStaticRepository.java" mode="EXCERPT">
````java
@Modifying
@Transactional
@Query(value = "create table cached_nightly_gl as select * from general_ledger where gl_year is not null", nativeQuery = true)
Integer createSnapshotCachedNightlyCopy();

@Modifying
@Transactional
@Query(value = "drop table if exists cached_nightly_gl", nativeQuery = true)
Integer dropExistingCachedNightlyCopy();
````
</augment_code_snippet>

**Problem Analysis:**
- **DDL Operations in Transactions:** CREATE TABLE and DROP TABLE operations can hold connections longer
- **Multiple Sequential Operations:** Service layer calls multiple repository methods sequentially
- **No Connection Pooling for DDL:** DDL operations may not benefit from connection pooling optimizations
- **Potential Deadlocks:** Multiple DDL operations across tenants can cause database locks

### 13. TenantIdentifierInterceptor Database Access - MEDIUM-HIGH RISK
**Location:** `src/main/java/com/nymbl/tenant/TenantIdentifierInterceptorAdapter.java:35-47`
**Risk Level:** MEDIUM-HIGH
**Pattern:** Database access in HTTP request interceptor

<augment_code_snippet path="src/main/java/com/nymbl/tenant/TenantIdentifierInterceptorAdapter.java" mode="EXCERPT">
````java
@Override
public boolean preHandle(HttpServletRequest req, HttpServletResponse res, Object handler) throws IOException {
    String authToken = req.getHeader(AuthoritiesConstants.X_AUTH_TOKEN);
    String username = tokenUtils.getUsernameFromToken(authToken);
    User user = username != null ? userRepository.findByUsername(username) : null;  // ❌ DB access in interceptor
    Company company = tokenUtils.getCompanyFromToken(authToken);
    // ... tenant context setup
}
````
</augment_code_snippet>

**Problem Analysis:**
- **Database Access Per Request:** Every HTTP request triggers a database lookup for user
- **No Connection Timeout:** Interceptor database access not subject to request timeouts
- **High Frequency:** Can generate thousands of database connections during peak load
- **No Caching:** User lookup happens on every request without caching

### 14. Spring Batch Schema Initialization Risk - MEDIUM RISK
**Location:** `src/main/resources/application-local.properties:93-94`
**Risk Level:** MEDIUM
**Pattern:** Automatic schema initialization enabled

<augment_code_snippet path="src/main/resources/application-local.properties" mode="EXCERPT">
````properties
spring.batch.jdbc.initialize-schema=ALWAYS
spring.batch.job.enabled=false
````
</augment_code_snippet>

**Problem Analysis:**
- **Schema Initialization on Startup:** Can hold connections during application startup
- **Multiple Tenant Initialization:** Each tenant may trigger schema operations
- **Startup Connection Storms:** All tenants initializing simultaneously during deployment

## 🚨 FINAL CRITICAL RISK ASSESSMENT

### Most Dangerous Patterns (Immediate Fix Required):

1. **ENABLE_LAZY_LOAD_NO_TRANS = true** - This is potentially the **ROOT CAUSE** of your connection leaks
   - Allows unlimited connection creation outside transaction boundaries
   - Each lazy property access can open new connections
   - Connections may not be properly managed by Spring

2. **DataDictionaryService Raw JDBC** - Guaranteed leak on any exception

3. **Spring Batch 50,000 Record Pages** - Can exhaust pools during batch operations

4. **Scheduled Job Connection Storms** - Multiple tenants × multiple jobs = connection exhaustion

### Connection Leak Multiplication Factors:

- **Multi-Tenant Architecture:** Each issue multiplied by number of active tenants
- **Lazy Loading:** Each entity access can trigger new connections
- **Batch Operations:** Large page sizes hold connections for extended periods
- **Cron Jobs:** Simultaneous execution across all tenants

## 🎯 FINAL IMMEDIATE ACTION PLAN

### CRITICAL (Deploy Today):
1. **Disable Lazy Loading Outside Transactions**
   ```properties
   # Change in both Master and Tenant configs
   org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS=false
   ```

2. **Fix DataDictionaryService** - Use try-with-resources

3. **Reduce Batch Page Sizes** - From 50,000 to 5,000

### HIGH PRIORITY (This Week):
4. **Add User Caching** - Cache user lookups in TenantIdentifierInterceptor
5. **Fix EntityManager Leaks** - All manual EntityManager creation patterns
6. **Optimize DDL Operations** - Review GeneralLedgerStaticRepository operations

### MONITORING (Immediate):
7. **Enable Connection Leak Detection**
   ```properties
   # HikariCP
   leakDetectionThreshold=60000
   # Tomcat JDBC
   logAbandoned=true
   ```

The **ENABLE_LAZY_LOAD_NO_TRANS** setting is likely your biggest culprit - it allows Hibernate to create connections outside of Spring's transaction management, which can easily lead to the exact symptoms you're experiencing.
