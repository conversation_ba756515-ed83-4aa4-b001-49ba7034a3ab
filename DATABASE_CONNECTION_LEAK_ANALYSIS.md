# Nymbl API Database Connection Leak Analysis

## Critical Findings Overview

| Severity | Issue Count | Primary Cause | Impact |
|----------|-------------|---------------|---------|
| CRITICAL | 5 | Hibernate lazy loading + manual resource management | Connection pool exhaustion |
| HIGH | 8 | EntityManager leaks + batch job issues | Memory leaks + connection retention |
| MEDIUM | 6 | Configuration issues + interceptor patterns | Intermittent connection issues |

## PRIMARY ISSUES IDENTIFIED

**Manual Resource Management** - Multiple patterns of EntityManager and JDBC connection handling that bypass proper resource cleanup, combined with potential connection pool pressure from lazy loading transaction churn.

## Production Symptoms Explained

- **New connections outpace cleanup** - Manual resource leaks bypass eviction mechanisms
- **Eviction not happening** - Leaked resources appear "active" to connection pools
- **Min evict idle settings ineffective** - Connections created outside pool management cannot be evicted

## CRITICAL CONNECTION LEAK VULNERABILITIES

### 1. DataDictionaryService Raw JDBC - IMMEDIATE THREAT

### 2. Hibernate Lazy Loading Transaction Churn - POTENTIAL CONTRIBUTING FACTOR
**Location:** `MasterDatabaseConfig.java` and `TenantDatabaseConfig.java`
**Risk Level:** MEDIUM-HIGH
**Issue Type:** Connection pool pressure, not direct leaks

```java
// BOTH Master and Tenant configurations contain this setting:
properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "true");
```

**Problem Analysis:**
- **Transaction Churn:** Each lazy property access creates a new transaction, increasing connection checkout/checkin cycles
- **Connection Pool Pressure:** High-frequency lazy loading during peak load could exhaust available connections
- **Failure Amplification:** If lazy-load transactions fail or timeout, connections might not be properly returned
- **Multi-Tenant Impact:** Lazy loading across multiple tenants simultaneously creates connection competition
- **Unpredictable Usage:** Makes connection usage patterns harder to predict and debug

**Note:** This setting creates new transactions (not necessarily new connections) but the increased connection churn could amplify existing leak issues.

### 3. Spring Batch Connection Pool Abuse - HIGH RISK
**Location:** `src/main/java/com/nymbl/config/service/DataDictionaryService.java:49-63`  
**Risk Level:** CRITICAL  
**Leak Probability:** 100% on any exception  

```java
public String generate() throws Exception {
    String fullURL = String.format(url.concat("&user=%s&password=%s"), username, password);
    Connection connection = DriverManager.getConnection(fullURL);  // NO try-with-resources
    Statement statement = connection.createStatement();
    ResultSet rs = statement.executeQuery(sql);
    // ... processing ...
    rs.close();      // Manual close - leaks if exception occurs
    statement.close();
    connection.close();
}
```

**Problem Analysis:**
- **Raw JDBC Outside Pool:** Bypasses all connection pool configurations and eviction settings
- **No Exception Handling:** Any exception between `getConnection()` and `connection.close()` causes permanent leak
- **Direct Database Access:** Creates connections directly to database without pool management
- **Guaranteed Leak Scenario:** 100% leak probability during exception conditions

### 3. Spring Batch Connection Pool Abuse - HIGH RISK
**Location:** Multiple Batch Configuration Classes  
**Risk Level:** HIGH  
**Pattern:** Direct DataSource access with massive page sizes  

```java
@Bean
@StepScope
public JdbcPagingItemReader<QuicksightPrescriptionSummary> qsPrescriptionSummaryJdbcPagingItemReader(@Value("#{jobParameters['tenant']}") String tenant) {
    JdbcPagingItemReader<QuicksightPrescriptionSummary> reader = new JdbcPagingItemReader<>();
    reader.setDataSource(MultiTenantConnectionProviderImpl.map.get(tenant));  // Direct map access
    reader.setPageSize(50000);  // MASSIVE page size
    reader.setFetchSize(50000); // MASSIVE fetch size
}
```

**Affected Classes:**
- `PrescriptionSummaryBatchConfiguration.java`
- `GeneralLedgerBatchConfiguration.java`
- `UserTaskBatchConfiguration.java`
- `AiNotesUsageBatchConfiguration.java`
- `NymblStatusHistoryBatchConfiguration.java`
- `PurchasingHistoryBatchConfiguration.java`
- `ClinicalOperationsBatchConfiguration.java`

**Problem Analysis:**
- **Direct DataSource Map Access:** Bypasses Spring's connection management
- **Massive Page/Fetch Sizes:** 50,000 records per page can hold connections for extended periods
- **No Connection Timeout Configuration:** Batch jobs can hold connections indefinitely
- **Multi-Tenant Amplification:** Each tenant runs separate batch jobs, multiplying connection usage

### 4. Scheduled Job Connection Storms - CRITICAL RISK
**Location:** `src/main/java/com/nymbl/config/cron/DailyJobExecutor.java`  
**Risk Level:** CRITICAL  
**Pattern:** Simultaneous multi-tenant operations without connection limits  

```java
List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
for (Company company : companies) {
    TenantContext.setCurrentTenant(company.getKey());
    prescriptionService.billDmeRentals(DateUtil.getCurrentDate());  // Heavy DB operations per tenant
    TenantContext.clear();
}
```

**Affected Scheduled Jobs:**
- `userPermissionsJob()` - 3:00 AM daily
- `patientRecallJob()` - Multiple timezone-based schedules
- `billDmeRentalsJob()` - Multiple timezone-based schedules
- `timelyFilingJob()` - Multiple timezone-based schedules
- `dueTasksJob()` - Multiple timezone-based schedules
- `clinicalOperationsJob()` - Multiple timezone-based schedules

**Problem Analysis:**
- **Connection Storm Pattern:** Multiple scheduled jobs run simultaneously across all tenants
- **No Connection Throttling:** Each tenant operation can consume full connection pool
- **Cascading Failures:** If one tenant's connections leak, it affects subsequent tenants
- **Peak Load Amplification:** All cron jobs trigger at same time zones

### 5. UserService Session Management - HIGH RISK
**Location:** `src/main/java/com/nymbl/master/service/UserService.java:1030-1045, 1054-1075`  
**Risk Level:** CRITICAL  
**Leak Probability:** High during exceptions  

```java
public List<QuicksightAssetUserPermissions> getUserAssetPermissions() {
    EntityManager em = masterEntityManager.getEntityManagerFactory().createEntityManager();
    Session session = em.unwrap(Session.class);
    session.doWork(connection -> {
        CallableStatement stmt = connection.prepareCall("{call userAssetPermissions()}");
        // ... processing ...
        connection.close();  // DANGEROUS: Manual close within doWork!
    });
    em.close();  // Not in try-with-resources
}
```

**Problem Analysis:**
- **Manual EntityManager Creation:** Without proper exception handling
- **Dangerous connection.close():** Within session.doWork() - Hibernate manages connection lifecycle
- **CallableStatement Leaks:** Not using try-with-resources
- **EntityManager Leak:** If exception occurs before em.close()

## HIGH-RISK PATTERNS IDENTIFIED

### 6. GeneralLedgerComparisonRepository - SYSTEMATIC LEAKS
**Location:** `src/main/java/com/nymbl/tenant/repository/GeneralLedgerComparisonRepository.java`  
**Risk Level:** HIGH  
**Affected Methods:** 8+ methods with identical pattern  

```java
public long getLastRevisionId() {
    EntityManager em = tenantEntityManager.getEntityManagerFactory().createEntityManager();
    // ... query execution ...
    em.close();  // Not in finally block or try-with-resources
    return result;
}
```

**Problem Analysis:**
- **Systematic Pattern:** Across multiple methods: `getQueryResultLongList()`, `checkPrescriptionRevisionId()`, `resetCachedLiveGL()`
- **EntityManager Creation:** Without exception safety
- **Transaction Management:** Without proper cleanup
- **Each Method:** Represents a potential leak point

### 7. AbstractTableService - KNOWN LEAK ACKNOWLEDGMENT
**Location:** `src/main/java/com/nymbl/config/service/AbstractTableService.java:407-415`  
**Risk Level:** HIGH  
**Evidence:** Developer comment indicates awareness  

```java
} catch (Exception e) {
    //em.close(); leaks...    // COMMENT SHOWS KNOWN ISSUE
    log.error(StringUtil.getExceptionAsString(e));
    Sentry.captureException(e);
}
```

**Problem Analysis:**
- **Known Issue:** Comment `//em.close(); leaks...` indicates developers are aware of leak issue
- **Multiple Methods:** In this class create EntityManager instances manually
- **Base Class:** Used by many services, multiplying impact
- **Unresolved:** Issue acknowledged but not fixed

### 8. TenantIdentifierInterceptor Database Access - MEDIUM-HIGH RISK
**Location:** `src/main/java/com/nymbl/tenant/TenantIdentifierInterceptorAdapter.java:35-47`  
**Risk Level:** MEDIUM-HIGH  
**Pattern:** Database access in HTTP request interceptor  

```java
@Override
public boolean preHandle(HttpServletRequest req, HttpServletResponse res, Object handler) throws IOException {
    String authToken = req.getHeader(AuthoritiesConstants.X_AUTH_TOKEN);
    String username = tokenUtils.getUsernameFromToken(authToken);
    User user = username != null ? userRepository.findByUsername(username) : null;  // DB access in interceptor
    Company company = tokenUtils.getCompanyFromToken(authToken);
    // ... tenant context setup
}
```

**Problem Analysis:**
- **Database Access Per Request:** Every HTTP request triggers a database lookup for user
- **No Connection Timeout:** Interceptor database access not subject to request timeouts
- **High Frequency:** Can generate thousands of database connections during peak load
- **No Caching:** User lookup happens on every request without caching

### 9. Connection Pool Configuration Issues
**Location:** `src/main/java/com/nymbl/config/MasterDatabaseConfig.java`  
**Risk Level:** MEDIUM  
**Pattern:** Extreme pool size differences between environments  

```java
if (Arrays.asList(environment.getActiveProfiles()).contains("local")) {
    config.setMaximumPoolSize(20);      // Local: 20 connections
    config.setMinimumIdle(5);
} else {
    config.setMaximumPoolSize(1_000);   // Production: 1,000 connections - EXCESSIVE
    config.setMinimumIdle(50);
}
```

**Problem Analysis:**
- **Massive Production Pool:** 1,000 max connections per master database
- **Per-Tenant Multiplication:** Each tenant also gets large pools
- **Resource Exhaustion Risk:** Database server may not support total connection load
- **Leak Amplification:** Large pools mask leak detection

### 10. Manual Transaction Management in Repositories - HIGH RISK
**Location:** `src/main/java/com/nymbl/tenant/repository/GeneralLedgerStaticRepository.java`  
**Risk Level:** HIGH  
**Pattern:** Multiple @Transactional @Modifying operations without proper connection management  

```java
@Modifying
@Transactional
@Query(value = "create table cached_nightly_gl as select * from general_ledger where gl_year is not null", nativeQuery = true)
Integer createSnapshotCachedNightlyCopy();

@Modifying
@Transactional
@Query(value = "drop table if exists cached_nightly_gl", nativeQuery = true)
Integer dropExistingCachedNightlyCopy();
```

**Problem Analysis:**
- **DDL Operations in Transactions:** CREATE TABLE and DROP TABLE operations can hold connections longer
- **Multiple Sequential Operations:** Service layer calls multiple repository methods sequentially
- **No Connection Pooling for DDL:** DDL operations may not benefit from connection pooling optimizations
- **Potential Deadlocks:** Multiple DDL operations across tenants can cause database locks

## LEAK IMPACT ANALYSIS

### Connection Pool Exhaustion Mechanics

1. **Lazy Loading Transaction Churn**
   - ENABLE_LAZY_LOAD_NO_TRANS creates new transactions for each lazy property access
   - Increased connection checkout/checkin cycles during high-frequency lazy loading
   - Connection pool pressure during peak load, especially across multiple tenants
   - Failed or timed-out lazy-load transactions may not properly return connections

2. **Manual Resource Leaks Override Pool Settings**
   - Eviction only works on idle connections
   - Leaked connections remain "active" in pool tracking
   - removeAbandoned cannot detect properly closed but leaked EntityManagers

3. **Multi-Tenant Amplification**
   - Each tenant has separate connection pool
   - Leak in one tenant affects that tenant's pool
   - Cross-tenant operations multiply leak potential
   - Batch jobs and cron jobs run per tenant, multiplying connection usage

4. **Exception-Driven Leak Acceleration**
   - Production exceptions trigger leak conditions
   - High-load scenarios increase exception probability
   - Leaked connections accumulate faster than eviction can clean

### Connection Leak Multiplication Factors

- **Multi-Tenant Architecture:** Each issue multiplied by number of active tenants
- **Lazy Loading:** Each entity access can trigger new connections
- **Batch Operations:** Large page sizes hold connections for extended periods
- **Cron Jobs:** Simultaneous execution across all tenants
- **HTTP Request Volume:** Database access in interceptors scales with traffic

## IMMEDIATE ACTION PLAN

### CRITICAL (Deploy Today):

1. **Fix DataDictionaryService Raw JDBC - HIGHEST PRIORITY**
   ```java
   public String generate() throws Exception {
       String fullURL = String.format(url.concat("&user=%s&password=%s"), username, password);
       try (Connection connection = DriverManager.getConnection(fullURL);
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql)) {
           // ... processing ...
           return buildHTML(dbResults);
       }
   }
   ```

2. **Reduce Batch Page Sizes**
   ```java
   // Change from 50,000 to 5,000 maximum in all batch configurations
   reader.setPageSize(5000);
   reader.setFetchSize(5000);
   ```

### HIGH PRIORITY (This Week):

3. **Fix UserService CallableStatement Patterns**
   ```java
   session.doWork(connection -> {
       try (CallableStatement stmt = connection.prepareCall("{call userAssetPermissions()}")) {
           // ... processing ...
           // REMOVE: connection.close(); - Let Hibernate manage!
       }
   });
   ```

4. **Fix GeneralLedgerComparisonRepository EntityManager Leaks**
   ```java
   public long getLastRevisionId() {
       try (EntityManager em = tenantEntityManager.getEntityManagerFactory().createEntityManager()) {
           // ... query execution ...
           return result;
       }
   }
   ```

5. **Add User Caching in TenantIdentifierInterceptor**
   - Implement user lookup caching to reduce database calls per request

6. **Audit Connection Pool Sizes**
   - Reduce production pool sizes from 1,000 to 100 and tune up based on monitoring

7. **Monitor Lazy Loading Impact**
   - Track connection pool utilization during high lazy-loading periods
   - Consider disabling ENABLE_LAZY_LOAD_NO_TRANS if connection churn becomes problematic

### MONITORING (Immediate):

8. **Enable Connection Leak Detection**
   ```properties
   # HikariCP
   leakDetectionThreshold=60000
   # Tomcat JDBC
   logAbandoned=true
   removeAbandonedTimeout=300
   ```

9. **Add Connection Pool Monitoring**
   ```properties
   # Add to application.properties
   logging.level.org.apache.tomcat.jdbc.pool=DEBUG
   logging.level.com.zaxxer.hikari=DEBUG
   ```

## 🔧 CONFIGURATION RECOMMENDATIONS

### Immediate Pool Tuning
```properties
# Reduce abandoned timeout for faster leak detection
removeAbandonedTimeout=300
# Enable abandoned connection logging
logAbandoned=true
# More aggressive eviction
timeBetweenEvictionRunsMillis=15000
minEvictableIdleTimeMillis=30000
```

### HikariCP Migration Priority
- HikariCP has superior leak detection and recovery
- Current local setting: `nymbl.database.hikari.enabled=true`
- Recommend production migration to HikariCP

## ADDITIONAL CRITICAL PATTERNS CAUSING GRADUAL CONNECTION ACCUMULATION

### 11. Exception Handling Without Resource Cleanup - CRITICAL GRADUAL LEAK
**Location:** Multiple service classes
**Risk Level:** CRITICAL
**Pattern:** Database operations in exception handlers without proper resource management

```java
// PaymentService.java - Exception handling with database operations
try {
    generalLedgerService2.insertUpdatePaymentEntry(payment);
} catch (Exception e) {
    Sentry.captureMessage("Payment # " + payment.getId() + " INSERT FAILURE - " + e.getCause().toString());
    // No resource cleanup in exception path
}

// StripeTransactionService.java - Exception swallowing with potential resource leaks
try {
    Specification<StripeTransaction> stripeTransactionSpecification = StripeTransactionSpecs.search(...);
    List<StripeTransaction> results = stripeTransactionRepository.findAll(stripeTransactionSpecification, pageable).getContent();
    return Optional.of(results.stream().map(x -> stripeUtil.convertToDto(x)).collect(Collectors.toList()));
} catch (Exception ex) {
    log.error("Stripe: Exception searching for patient payments ", ex);
    // Exception swallowed - any open resources not cleaned up
}
return Optional.empty();
```

**Problem Analysis:**
- **Exception Swallowing:** Exceptions caught and logged but resources may not be properly cleaned up
- **Database Operations in Exception Handlers:** Additional database calls in catch blocks without resource management
- **Gradual Accumulation:** Each exception during normal operations can leak connections slowly throughout the day
- **High Frequency:** Payment processing and search operations happen frequently

### 12. Long-Running Transactional Operations - CONNECTION RETENTION
**Location:** Multiple repository classes
**Risk Level:** HIGH
**Pattern:** @Transactional @Modifying operations that can hold connections for extended periods

```java
// ClinicalOperationsRepository.java
@Modifying
@Transactional
@Query(value = "DELETE FROM `multitenant`.`clinical_operations` WHERE tenant = :tenant", nativeQuery = true)
void removeStaleTenantData(@Param("tenant") String tenant);

@Modifying
@Transactional
@Query(value = importTenantQuery, nativeQuery = true)
void importClinicalOperationsData(@Param("tenant") String tenant);

// GeneralLedgerRepository.java
@Modifying
@Transactional
@Query(value = "TRUNCATE TABLE general_ledger", nativeQuery = true)
void truncateGeneralLedgerTable();
```

**Problem Analysis:**
- **DDL Operations:** TRUNCATE and large DELETE operations can hold connections for minutes
- **Cross-Database Operations:** Operations on `multitenant` database may use different connection pools
- **No Timeout Configuration:** These operations can run indefinitely during high load
- **Tenant Multiplication:** Each tenant runs these operations, multiplying connection usage

### 13. Streaming and Pagination Without Connection Management - GRADUAL ACCUMULATION
**Location:** Multiple service classes
**Risk Level:** MEDIUM-HIGH
**Pattern:** Large result set processing that may hold connections during iteration

```java
// PrescriptionSummaryService.java - Large result set processing
List<Prescription> prescriptions = findPrescriptionDm(prescriptionsHeader, querySegment, fieldNameAndDirection[0], fieldNameAndDirection[1], offset, export);
List<PrescriptionView> prescriptionViews = prescriptions.stream().map(PrescriptionView::new).collect(Collectors.toList());

for (PrescriptionView prescription : prescriptionViews) { // Large iteration over database results
    // ... processing each prescription ...
}

// TaskService.java - Complex queries with multiple database calls
List<Long> employeeIds = userService.getActiveEmployeesByManagerId(userId).stream()
    .map(u -> u.getId())
    .collect(Collectors.toList());
List<Long> userRoles = userRoleRepository.findByUserId(userId).stream()
    .map(UserRole::getRoleId)
    .collect(Collectors.toList());
```

**Problem Analysis:**
- **Large Result Sets:** Processing thousands of records can hold connections during iteration
- **Multiple Database Calls:** Sequential database operations within loops
- **Memory Pressure:** Large collections loaded into memory while connections remain open
- **No Streaming:** Results loaded entirely into memory rather than streamed

### 14. Tenant Context Switching Without Connection Cleanup - CRITICAL PATTERN
**Location:** `DailyJobExecutor.java` and similar cron jobs
**Risk Level:** CRITICAL
**Pattern:** Rapid tenant switching that may not properly clean up connections

```java
// Pattern repeated across multiple cron jobs
List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
for (Company c : companies) {
    TenantContext.setCurrentTenant(c.getKey());  // Switch tenant
    // Heavy database operations...
    quicksightExportService.uploadGeneralLedgerCsvS3Quicksight();
    generalLedgerService.importGeneralLedgerHistoryToMultitenant();
    generalLedgerService.triggerGLToAws();
    TenantContext.clear();  // Clear tenant context
}
```

**Problem Analysis:**
- **Rapid Context Switching:** Switching between tenant databases rapidly
- **Connection Pool Confusion:** Each tenant switch may not properly return connections to correct pool
- **Heavy Operations Per Tenant:** Each tenant performs multiple heavy database operations
- **No Connection Verification:** No verification that connections are properly returned after tenant switch
- **Multiplied Impact:** Pattern repeated across 6+ different cron jobs

## GRADUAL LEAK ACCELERATION FACTORS

### Daily Accumulation Patterns:
1. **Exception-Driven Leaks:** Every exception in payment processing, search operations, etc. can leak connections
2. **Cron Job Amplification:** Multiple daily jobs across all tenants create connection pressure
3. **Long-Running Operations:** DDL operations and large data imports hold connections for extended periods
4. **Tenant Switching Overhead:** Rapid tenant context changes may not properly clean up connections

### Why Connections Outpace Cleanup:
- **Exception Frequency:** Production exceptions occur throughout the day, each potentially leaking connections
- **Batch Operation Timing:** Large operations during business hours compete with regular traffic
- **Multi-Tenant Multiplication:** Every leak pattern multiplied by number of active tenants
- **Connection Pool Confusion:** Tenant switching may cause connections to be returned to wrong pools
